import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './sidebar.css';

interface SidebarProps {
  isCollapsed: boolean;
  isMobileOpen: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, isMobileOpen }) => {
  const location = useLocation();

  return (
    <nav className={`sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`}>
      <div className="sidebar-header">
        <Link to="/" className="sidebar-logo">
          <div className="logo-container">
            <img src="assets/logoforSoundscape.jpg" alt="SoundScape Logo" className="sidebar-logo-image" />
            {!isCollapsed && <span className="logo-text">SoundScape</span>}
          </div>
        </Link>
      </div>

      <div className="sidebar-section">
        <Link to="/" className={`sidebar-item ${location.pathname === '/' ? 'active' : ''}`} title="Home">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Home</span>}
        </Link>
        <Link to="/explore" className={`sidebar-item ${location.pathname === '/explore' ? 'active' : ''}`} title="Explore">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 10.9c-.61 0-1.1.49-1.1 1.1s.49 1.1 1.1 1.1c.61 0 1.1-.49 1.1-1.1s-.49-1.1-1.1-1.1zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm2.19 12.19L6 18l3.81-8.19L18 6l-3.81 8.19z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Explore</span>}
        </Link>
        <Link to="/genres" className={`sidebar-item ${location.pathname.startsWith('/genres') ? 'active' : ''}`} title="Library">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Library</span>}
        </Link>
      </div>

      {!isCollapsed && <div className="sidebar-divider"></div>}

      <div className="sidebar-section">
        <Link to="/about" className={`sidebar-item ${location.pathname === '/about' ? 'active' : ''}`} title="About">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>About</span>}
        </Link>
      </div>
    </nav>
  );
};

export default Sidebar;