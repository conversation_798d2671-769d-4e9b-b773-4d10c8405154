/* GenreList Page Styles */
.genrelist-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.genrelist-header {
  margin-bottom: 30px;
  text-align: center;
}

.genrelist-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.genrelist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.genre-tile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 140px;
  border-radius: 10px;
  color: white;
  text-decoration: none;
  transition: transform 0.2s ease;
  text-align: center;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.genre-tile:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.genre-icon {
  font-size: 2.2rem;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.genre-tile:hover .genre-icon {
  transform: scale(1.1);
}

.genre-tile h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.genre-featured {
  margin-top: 40px;
}

.genre-featured h2 {
  text-align: center;
  margin-bottom: 20px;
}

.featured-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 30px;
}

.featured-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.featured-card:hover {
  transform: translateY(-5px);
}

.featured-image {
  height: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.featured-image span {
  font-size: 3.5rem;
}

.featured-card h3 {
  margin: 15px 15px 10px;
  font-size: 1.3rem;
}

.featured-card p {
  margin: 0 15px 15px;
  color: #555;
}

.genrelist-loading,
.genrelist-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.genrelist-error button {
  margin-top: 20px;
  padding: 8px 16px;
  border: none;
  background-color: #3498db;
  color: white;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-indicator,
.error-indicator {
  display: inline-block;
  margin-left: 10px;
  font-style: italic;
}

.error-indicator {
  color: #e74c3c;
}

.no-genres {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #777;
  font-style: italic;
}