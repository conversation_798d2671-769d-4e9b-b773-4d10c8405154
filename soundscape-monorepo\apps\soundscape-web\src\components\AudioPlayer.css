.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  padding: 16px;
  color: var(--text-primary);
  z-index: 1000;
  box-shadow: 0 -2px 10px var(--shadow);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.music-player-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.player-left {
  display: flex;
  align-items: center;
  width: 30%;
}

.album-art {
  width: 56px;
  height: 56px;
  margin-right: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song-info h3 {
  margin: 0;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  color: var(--text-primary);
}

.song-info p {
  margin: 4px 0 0;
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.player-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.control-button {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  transition: transform 0.2s;
}

.control-button:hover:not(:disabled) {
  transform: scale(1.1);
  color: var(--accent-primary);
}

.control-button:disabled {
  cursor: default;
  opacity: 0.5;
  color: var(--text-secondary);
}

.play-pause {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--text-primary);
  color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, transform 0.2s;
}

.play-pause:hover {
  background: var(--accent-primary);
  color: var(--bg-primary);
}

.progress-container {
  width: 100%;
  max-width: 600px;
  padding: 0 20px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.progress {
  height: 100%;
  background: var(--accent-primary);
  border-radius: 2px;
  transition: width 0.1s linear;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .player-left {
    width: 40%;
  }

  .album-art {
    width: 40px;
    height: 40px;
  }

  .song-info h3 {
    font-size: 12px;
  }

  .song-info p {
    font-size: 10px;
  }

  .player-controls {
    gap: 8px;
  }

  .play-pause {
    width: 32px;
    height: 32px;
  }

  .progress-container {
    max-width: 100%;
    padding: 0 10px;
  }
}