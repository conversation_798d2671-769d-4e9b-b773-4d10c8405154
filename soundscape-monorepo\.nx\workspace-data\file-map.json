{"version": "6.0", "nxVersion": "20.4.6", "pathMappings": {"@soundscape-monorepo/soundscape-monorepo-redux": ["soundscape-monorepo-redux/src/index.ts"]}, "nxJsonPlugins": [{"name": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/jest/plugin", "options": {"targetName": "test"}}], "fileMap": {"projectFileMap": {"@soundscape-monorepo/source": [{"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": ".github/workflows/ci.yml", "hash": "15142501639632954735"}, {"file": ".giti<PERSON>re", "hash": "17737885269298436116"}, {"file": ".prettieri<PERSON>re", "hash": "14688272390704683536"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": ".verdaccio/config.yml", "hash": "300035921567345902"}, {"file": ".vscode/extensions.json", "hash": "11309362834336059819"}, {"file": "README.md", "hash": "11154986424148592146"}, {"file": "eslint.config.mjs", "hash": "3462096019013144053", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "jest.config.ts", "hash": "3494843969422251966"}, {"file": "jest.preset.js", "hash": "9430166341120122740"}, {"file": "nx.json", "hash": "5548839828748130999"}, {"file": "package-lock.json", "hash": "3892307793853950426"}, {"file": "package.json", "hash": "10845076516590521815", "deps": ["npm:@babel/core", "npm:@babel/preset-react", "npm:@eslint/js", "npm:@nx/devkit", "npm:@nx/eslint", "npm:@nx/eslint-plugin", "npm:@nx/jest", "npm:@nx/js", "npm:@nx/playwright", "npm:@nx/react", "npm:@nx/vite", "npm:@nx/web", "npm:@nx/workspace", "npm:@playwright/test", "npm:@swc-node/register", "npm:@swc/cli", "npm:@swc/core", "npm:@swc/helpers", "npm:@testing-library/dom", "npm:@testing-library/react", "npm:@types/jest", "npm:@types/node", "npm:@types/react", "npm:@types/react-dom", "npm:@vitejs/plugin-react", "npm:@vitest/coverage-v8", "npm:@vitest/ui", "npm:babel-jest", "npm:eslint", "npm:eslint-config-prettier", "npm:eslint-plugin-import", "npm:eslint-plugin-jsx-a11y", "npm:eslint-plugin-playwright", "npm:eslint-plugin-react", "npm:eslint-plugin-react-hooks", "npm:jest", "npm:jest-environment-jsdom", "npm:jsdom", "npm:nx", "npm:prettier", "npm:ts-jest", "npm:ts-node", "npm:tslib", "npm:typescript", "npm:typescript-eslint", "npm:ve<PERSON><PERSON><PERSON>", "npm:vite", "npm:vite-plugin-dts", "npm:vitest", "npm:@nrwl/react", "npm:@types/redux-logger", "npm:react", "npm:react-dom", "npm:react-redux", "npm:react-router-dom", "npm:redux-logger"]}, {"file": "project.json", "hash": "15668333190429064870"}, {"file": "tsconfig.base.json", "hash": "15996199527936782030"}, {"file": "vitest.workspace.ts", "hash": "14023491222425291751"}], "soundscape-monorepo-redux": [{"file": "soundscape-monorepo-redux/.babelrc", "hash": "2946678488919325452"}, {"file": "soundscape-monorepo-redux/README.md", "hash": "8621679974979382883"}, {"file": "soundscape-monorepo-redux/eslint.config.mjs", "hash": "6286113312110665026", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "soundscape-monorepo-redux/jest.config.ts", "hash": "3744029698987331649"}, {"file": "soundscape-monorepo-redux/package-lock.json", "hash": "12383462687948523055"}, {"file": "soundscape-monorepo-redux/package.json", "hash": "7862856011073537853"}, {"file": "soundscape-monorepo-redux/project.json", "hash": "6187489747176911395"}, {"file": "soundscape-monorepo-redux/src/actions.ts", "hash": "184498894447814419"}, {"file": "soundscape-monorepo-redux/src/actions/authActions.ts", "hash": "3995731610824415391"}, {"file": "soundscape-monorepo-redux/src/hooks/redux-hooks.ts", "hash": "10529490178604271751", "deps": ["npm:react-redux"]}, {"file": "soundscape-monorepo-redux/src/hooks/use-hook.ts", "hash": "2672124412297743318"}, {"file": "soundscape-monorepo-redux/src/index.ts", "hash": "18008209653169237806"}, {"file": "soundscape-monorepo-redux/src/lib/soundscape-monorepo-redux.module.css", "hash": "125145030238681982"}, {"file": "soundscape-monorepo-redux/src/lib/soundscape-monorepo-redux.spec.tsx", "hash": "5914334933467790599", "deps": ["npm:@testing-library/react"]}, {"file": "soundscape-monorepo-redux/src/lib/soundscape-monorepo-redux.tsx", "hash": "11110244269124215697"}, {"file": "soundscape-monorepo-redux/src/reducers.ts", "hash": "3984519747699189550", "deps": ["npm:redux"]}, {"file": "soundscape-monorepo-redux/src/reducers/authReducer.ts", "hash": "17096618195791597145"}, {"file": "soundscape-monorepo-redux/src/reducers/counterReducer.ts", "hash": "6133173107694247980"}, {"file": "soundscape-monorepo-redux/src/reducers/genresReducer.ts", "hash": "13074668793264385838"}, {"file": "soundscape-monorepo-redux/src/reducers/musicPlayerReducer.ts", "hash": "11012517307348385209"}, {"file": "soundscape-monorepo-redux/src/reducers/userReducer.ts", "hash": "7790659320169181547"}, {"file": "soundscape-monorepo-redux/src/services/authService.ts", "hash": "11270536671436588336"}, {"file": "soundscape-monorepo-redux/src/services/genreService.ts", "hash": "12190708340883993764"}, {"file": "soundscape-monorepo-redux/src/slices/genreSlice.ts", "hash": "3183745761844274814"}, {"file": "soundscape-monorepo-redux/src/slices/playerSlice.ts", "hash": "13973311070659193604"}, {"file": "soundscape-monorepo-redux/src/store.ts", "hash": "18002609526734156127", "deps": ["npm:redux-logger"]}, {"file": "soundscape-monorepo-redux/src/thunks/authThunks.ts", "hash": "16862937505262260013", "deps": ["npm:redux"]}, {"file": "soundscape-monorepo-redux/src/thunks/genreThunks.ts", "hash": "17955524745780233891"}, {"file": "soundscape-monorepo-redux/tsconfig.json", "hash": "11610503207813775271"}, {"file": "soundscape-monorepo-redux/tsconfig.lib.json", "hash": "15701061882775745735"}, {"file": "soundscape-monorepo-redux/tsconfig.spec.json", "hash": "6522355885063527494"}, {"file": "soundscape-monorepo-redux/vite.config.ts", "hash": "9809591585689339998", "deps": ["npm:vite", "npm:@vitejs/plugin-react", "npm:@nx/vite"]}], "soundscape-web-e2e": [{"file": "apps/soundscape-web-e2e/eslint.config.mjs", "hash": "1172734138943286526", "deps": ["npm:eslint-plugin-playwright"]}, {"file": "apps/soundscape-web-e2e/playwright.config.ts", "hash": "9608298594049162837", "deps": ["npm:@playwright/test", "npm:@nx/playwright", "npm:@nx/devkit"]}, {"file": "apps/soundscape-web-e2e/project.json", "hash": "12149139277351093007"}, {"file": "apps/soundscape-web-e2e/src/example.spec.ts", "hash": "16019101014373124625", "deps": ["npm:@playwright/test"]}, {"file": "apps/soundscape-web-e2e/tsconfig.json", "hash": "18107275616570252607"}], "soundscape-web": [{"file": "apps/soundscape-web/assets/logoforSoundscape.jpg", "hash": "5904774759592956811"}, {"file": "apps/soundscape-web/build/soundscape-web/assets/index-DrcERb7e.css", "hash": "2440182829235777082"}, {"file": "apps/soundscape-web/build/soundscape-web/assets/index-f0DHL09p.js", "hash": "11608924566794077538"}, {"file": "apps/soundscape-web/build/soundscape-web/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/soundscape-web/build/soundscape-web/index.html", "hash": "5231117403850169606"}, {"file": "apps/soundscape-web/eslint.config.mjs", "hash": "8789163670399984295", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/soundscape-web/index.html", "hash": "13567268795788591441"}, {"file": "apps/soundscape-web/project.json", "hash": "16167814114378348765"}, {"file": "apps/soundscape-web/public/assets/logoforSoundscape.jpg", "hash": "13954086135572056333"}, {"file": "apps/soundscape-web/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/soundscape-web/src/App.tsx", "hash": "2171465855654142211", "deps": ["npm:react-router-dom"]}, {"file": "apps/soundscape-web/src/app.css", "hash": "6454635390338002478"}, {"file": "apps/soundscape-web/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/soundscape-web/src/components/AudioPlayer.css", "hash": "14367656628538933360"}, {"file": "apps/soundscape-web/src/components/AudioPlayer.tsx", "hash": "7845988503057877144", "deps": ["npm:react", "npm:react-redux", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/components/MusicPlayer.css", "hash": "17936086040489401319"}, {"file": "apps/soundscape-web/src/components/MusicPlayer.tsx", "hash": "815623719192835927", "deps": ["npm:react", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/components/Navbar.tsx", "hash": "4708664402532233717", "deps": ["npm:react", "npm:react-router-dom", "npm:react-redux", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/components/ProtectedRoute.tsx", "hash": "3794255245580940558", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "apps/soundscape-web/src/components/Sidebar.tsx", "hash": "4605370217083882668", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "apps/soundscape-web/src/components/StreamPlayer.css", "hash": "4260207312225564650"}, {"file": "apps/soundscape-web/src/components/StreamPlayer.tsx", "hash": "9273678992809001068", "deps": ["npm:react", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/components/navbar.css", "hash": "13798148707236924324"}, {"file": "apps/soundscape-web/src/components/sidebar.css", "hash": "7658241979076974489"}, {"file": "apps/soundscape-web/src/context/MusicPlayerContext.tsx", "hash": "3486710580654932955", "deps": ["npm:react"]}, {"file": "apps/soundscape-web/src/main.tsx", "hash": "13735691582365091678", "deps": ["npm:react", "npm:react-dom", "npm:react-router-dom", "npm:react-redux", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/pages/About.tsx", "hash": "8768248678876406447", "deps": ["npm:react"]}, {"file": "apps/soundscape-web/src/pages/Account.tsx", "hash": "13610168757609212097", "deps": ["npm:react", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/pages/Explore.tsx", "hash": "6962697619116002466", "deps": ["npm:react"]}, {"file": "apps/soundscape-web/src/pages/GenreList.tsx", "hash": "15416013921295881329", "deps": ["npm:react", "npm:react-router-dom", "npm:react-redux", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/pages/Home.tsx", "hash": "12791695669467898302", "deps": ["npm:react", "npm:react-router-dom", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/pages/Login.tsx", "hash": "3981186533280691985", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "apps/soundscape-web/src/pages/NotFound.tsx", "hash": "16381356974875035330", "deps": ["npm:react"]}, {"file": "apps/soundscape-web/src/pages/SelectedGenre.tsx", "hash": "17964352160241305886", "deps": ["npm:react", "npm:react-router-dom", "npm:react-redux", "soundscape-monorepo-redux"]}, {"file": "apps/soundscape-web/src/pages/Signup.tsx", "hash": "14008714545951052170", "deps": ["npm:react", "npm:react-router-dom"]}, {"file": "apps/soundscape-web/src/pages/account.css", "hash": "17100851711902524960"}, {"file": "apps/soundscape-web/src/pages/auth.css", "hash": "13067616441648832069"}, {"file": "apps/soundscape-web/src/pages/genrelist.css", "hash": "12960286477105706716"}, {"file": "apps/soundscape-web/src/pages/home.css", "hash": "7798526943760547421"}, {"file": "apps/soundscape-web/src/pages/selectedgenre.css", "hash": "1779929933373430119"}, {"file": "apps/soundscape-web/src/styles.css", "hash": "11917943937210195513"}, {"file": "apps/soundscape-web/tsconfig.app.json", "hash": "3374933587361460334"}, {"file": "apps/soundscape-web/tsconfig.json", "hash": "11166545116916107487"}, {"file": "apps/soundscape-web/tsconfig.spec.json", "hash": "2374680567064402239"}, {"file": "apps/soundscape-web/vite.config.ts", "hash": "8615043649273909231", "deps": ["npm:vite", "npm:@vitejs/plugin-react", "npm:@nx/vite"]}]}, "nonProjectFiles": []}}