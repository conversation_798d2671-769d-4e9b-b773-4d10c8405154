.sidebar {
  position: fixed;
  top: 56px;
  left: 0;
  bottom: 80px;
  width: 240px;
  background: #0f0f0f;
  padding: 0;
  overflow-y: auto;
  z-index: 99;
  border-right: 1px solid #333;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 72px;
  overflow: hidden;
}

/* Sidebar header removed - logo moved to navbar */

.sidebar-section {
  padding: 16px 0 8px 0;
}

.sidebar-section:first-child {
  padding-top: 16px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  text-decoration: none;
  color: #aaa;
  gap: 24px;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
}

.sidebar.collapsed .sidebar-item {
  padding: 0 20px;
  justify-content: center;
  gap: 0;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-right: 3px solid #ff0000;
}

.sidebar.collapsed .sidebar-item.active {
  border-right: 3px solid #ff0000;
  border-radius: 0 12px 12px 0;
}

.sidebar-item svg {
  width: 24px;
  height: 24px;
  color: inherit;
  flex-shrink: 0;
}

.sidebar-item span {
  font-size: 14px;
  font-weight: 400;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}

.sidebar.collapsed .sidebar-item span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Tooltip for collapsed sidebar items */
.sidebar.collapsed .sidebar-item {
  position: relative;
}

.sidebar.collapsed .sidebar-item::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #333;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-left: 8px;
  z-index: 1000;
}

.sidebar.collapsed .sidebar-item:hover::after {
  opacity: 1;
}

.sidebar-divider {
  height: 1px;
  background: #333;
  margin: 8px 0;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }
}