.selected-genre-page {
  width: 100%;
  height: 100%;
}

.genre-header {
  padding: 40px 20px;
  color: white;
  position: relative;
}

.back-button {
  color: white;
  text-decoration: none;
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.genre-header-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.genre-icon {
  font-size: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.genre-header-content:hover .genre-icon {
  transform: scale(1.05);
}

.genre-header h1 {
  font-size: 3rem;
  margin: 0 0 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.genre-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.genre-channels,
.genre-popular-tracks,
.genre-featured-artists,
.genre-all-tracks {
  margin-bottom: 40px;
}

.genre-channels h2,
.genre-popular-tracks h2,
.genre-featured-artists h2,
.genre-all-tracks h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

/* Channels grid styles */
.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.channel-card {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
  overflow: hidden;
}

.channel-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.channel-image {
  height: 140px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.channel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  font-size: 3rem;
  color: #888;
}

.channel-card h3 {
  margin: 0 0 8px;
  font-size: 1.1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.channel-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 2.8em;
}

.error {
  color: #e53935;
  font-weight: 500;
}

.tracks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.track-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  transition: background-color 0.2s;
  cursor: pointer;
}

.track-item:hover {
  background-color: #f0f0f0;
}

.track-info h3 {
  margin: 0 0 4px;
  font-size: 1.1rem;
}

.track-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.track-duration {
  color: #888;
  font-size: 0.9rem;
}

.artists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
}

.artist-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s;
}

.artist-card:hover {
  transform: translateY(-5px);
}

.artist-image {
  font-size: 2.5rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.artist-card h3 {
  margin: 0 0 5px;
  font-size: 1.1rem;
}

.artist-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.genre-not-found {
  text-align: center;
  padding: 60px 20px;
}

.genre-not-found h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}

.genre-not-found p {
  margin-bottom: 20px;
  color: #666;
}