.sidebar {
  position: fixed;
  top: 56px;
  left: 0;
  bottom: 80px;
  width: 240px;
  background: #0f0f0f;
  padding: 0;
  overflow-y: auto;
  z-index: 99;
  border-right: 1px solid #333;
}

.sidebar-header {
  padding: 16px 24px;
  border-bottom: 1px solid #333;
}

.sidebar-logo {
  text-decoration: none;
  color: #fff;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo-image {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.logo-text {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}

.sidebar-section {
  padding: 8px 0;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  text-decoration: none;
  color: #aaa;
  gap: 24px;
  transition: all 0.2s ease;
  border-radius: 0;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-right: 3px solid #ff0000;
}

.sidebar-item svg {
  width: 24px;
  height: 24px;
  color: inherit;
}

.sidebar-item span {
  font-size: 14px;
  font-weight: 400;
}

.sidebar-divider {
  height: 1px;
  background: #333;
  margin: 8px 0;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }
}