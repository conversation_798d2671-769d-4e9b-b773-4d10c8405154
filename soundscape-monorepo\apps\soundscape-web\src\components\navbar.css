.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  padding: 0 16px;
  justify-content: space-between;
  border-bottom: 1px solid #333;
  z-index: 100;
  color: #fff;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-button {
  padding: 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  color: #fff;
}

.menu-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

.logo-image {
  height: 40px;
  width: auto;
  border-radius: 4px;
}

.logo-image-small {
  height: 30px;
  width: auto;
  border-radius: 3px;
}

.search-bar {
  flex: 0 1 732px;
  margin: 0 40px;
}

.search-bar form {
  display: flex;
  height: 40px;
}

.search-bar input {
  flex: 1;
  padding: 0 16px;
  border: 1px solid #333;
  border-radius: 2px 0 0 2px;
  font-size: 16px;
  background: #2a2a2a;
  color: #fff;
}

.search-bar input::placeholder {
  color: #999;
}

.search-bar button {
  width: 64px;
  border: 1px solid #333;
  border-left: none;
  border-radius: 0 2px 2px 0;
  background: #2a2a2a;
  cursor: pointer;
  color: #fff;
}

.search-bar button:hover {
  background: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-button {
  padding: 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  color: #fff;
  transition: background-color 0.2s;
}

.icon-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Drawer styles */
.drawer {
  position: fixed;
  top: 0;
  left: -280px;
  height: 100vh;
  width: 280px;
  background: #1a1a1a;
  z-index: 200;
  transition: transform 0.3s ease;
  color: #fff;
}

.drawer-open {
  transform: translateX(280px);
}

.drawer-content {
  padding: 12px 0;
  margin-top: 56px;
}

.drawer-item {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  text-decoration: none;
  color: #fff;
  gap: 24px;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  transition: background-color 0.2s;
}

.drawer-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.drawer-item svg {
  width: 24px;
  height: 24px;
  color: #fff;
}

.drawer-item span {
  font-size: 14px;
  color: #fff;
}

/* Overlay */
.drawer-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 199;
}

.drawer-open + .drawer-overlay {
  display: block;
}

/* Add these styles to navbar.css */

.drawer-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 56px;
  border-bottom: 1px solid #333;
}

.drawer-close-button {
  background: none;
  border: none;
  padding: 8px;
  margin-right: 16px;
  cursor: pointer;
  border-radius: 50%;
  color: #fff;
}

.drawer-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.drawer-logo {
  font-size: 18px;
  font-weight: bold;
  text-decoration: none;
  color: #fff;
}

.drawer-content {
  padding-top: 0;
  margin-top: 0;
}

/* Add desktop navigation styles */
.desktop-nav {
  display: none;
  margin-left: 20px;
}

.nav-link {
  text-decoration: none;
  color: #fff;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Show desktop nav on larger screens */
@media (min-width: 1024px) {
  .desktop-nav {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .search-bar {
    margin: 0 20px;
  }
}

.nav-auth {
  display: flex;
  align-items: center;
  gap: 16px;
}

.login-link,
.signup-link,
.account-link {
  text-decoration: none;
  color: #fff;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  transition: background-color 0.2s;
}

.login-link {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.signup-link {
  background: #2196f3;
  border: 1px solid #2196f3;
}

.login-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

.signup-link:hover {
  background: #1976d2;
}

.logout-button {
  background: #f44336;
  border: none;
  color: #fff;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background: #d32f2f;
}