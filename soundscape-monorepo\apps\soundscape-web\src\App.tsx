import { Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import AudioPlayer from './components/AudioPlayer';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import About from './pages/About';
import NotFound from './pages/NotFound';
import Explore from './pages/Explore';
import Account from './pages/Account';
import GenreList from './pages/GenreList';
import SelectedGenre from './pages/SelectedGenre';
import Login from './pages/Login';
import Signup from './pages/Signup';
import './app.css';
import MusicPlayer from './components/MusicPlayer';

function App() {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';

  return (
    <div className="App">
      <Navbar />
      <div className="app-layout">
        <Sidebar />
        <main className="main-content">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            } />
            <Route path="/about" element={
              <ProtectedRoute>
                <About />
              </ProtectedRoute>
            } />
            <Route path="/explore" element={
              <ProtectedRoute>
                <Explore />
              </ProtectedRoute>
            } />
            <Route path="/genres" element={
              <ProtectedRoute>
                <GenreList />
              </ProtectedRoute>
            } />
            <Route path="/genres/:genreId" element={
              <ProtectedRoute>
                <SelectedGenre />
              </ProtectedRoute>
            } />

            {/* Protected Routes - require authentication */}
            <Route path="/account" element={
              <ProtectedRoute requireAuth>
                <Account />
              </ProtectedRoute>
            } />

            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
      {/* <MusicPlayer /> */}
      <AudioPlayer />
    </div>
  );
}

export default App;