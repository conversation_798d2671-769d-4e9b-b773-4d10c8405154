.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  padding: 0 16px;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  z-index: 100;
  color: var(--text-primary);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 240px;
}

.menu-button {
  padding: 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  color: var(--text-primary);
  display: block;
}

.menu-button:hover {
  background: var(--hover-bg);
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 16px;
}

.header-logo-image {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.header-logo-text {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  text-decoration: none;
}

/* Adjust header left width on mobile */
@media (max-width: 768px) {
  .header-left {
    width: auto;
  }
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
}

.logo-image {
  height: 40px;
  width: auto;
  border-radius: 4px;
}

.logo-image-small {
  height: 30px;
  width: auto;
  border-radius: 3px;
}

.search-bar {
  flex: 1;
  max-width: 600px;
  margin: 0 40px;
}

.search-bar form {
  display: flex;
  height: 40px;
  background: var(--bg-secondary);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.search-bar input {
  flex: 1;
  padding: 0 16px;
  border: none;
  font-size: 14px;
  background: transparent;
  color: var(--text-primary);
  outline: none;
}

.search-bar input::placeholder {
  color: var(--text-muted);
}

.search-bar button {
  width: 64px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-bar button:hover {
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-button {
  padding: 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  color: var(--text-primary);
  transition: background-color 0.2s;
}

.icon-button:hover {
  background: var(--hover-bg);
}

/* Drawer styles */
.drawer {
  position: fixed;
  top: 0;
  left: -280px;
  height: 100vh;
  width: 280px;
  background: var(--bg-secondary);
  z-index: 200;
  transition: transform 0.3s ease;
  color: var(--text-primary);
}

.drawer-open {
  transform: translateX(280px);
}

.drawer-content {
  padding: 0;
  margin-top: 56px;
}

.drawer-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.drawer-logo-image {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.drawer-logo-text {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
}

.drawer-item {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  text-decoration: none;
  color: var(--text-secondary);
  gap: 24px;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  transition: all 0.2s ease;
}

.drawer-item:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.drawer-item svg {
  width: 24px;
  height: 24px;
  color: inherit;
}

.drawer-item span {
  font-size: 14px;
  color: inherit;
}

/* Overlay */
.drawer-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay);
  z-index: 199;
}

.drawer-open + .drawer-overlay {
  display: block;
}

/* Add these styles to navbar.css */

.drawer-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  height: 56px;
  border-bottom: 1px solid #333;
}

.drawer-close-button {
  background: none;
  border: none;
  padding: 8px;
  margin-right: 16px;
  cursor: pointer;
  border-radius: 50%;
  color: #fff;
}

.drawer-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.drawer-logo {
  font-size: 18px;
  font-weight: bold;
  text-decoration: none;
  color: #fff;
}

.drawer-content {
  padding-top: 0;
  margin-top: 0;
}

/* Overlay for mobile drawer */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay);
  z-index: 150;
}

/* Desktop navigation removed - now using sidebar */

.nav-auth {
  display: flex;
  align-items: center;
  gap: 16px;
}

.login-link,
.signup-link,
.account-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  transition: background-color 0.2s;
}

.login-link {
  border: 1px solid var(--border-color);
}

.signup-link {
  background: var(--accent-secondary);
  border: 1px solid var(--accent-secondary);
}

.login-link:hover {
  background: var(--hover-bg);
}

.signup-link:hover {
  background: var(--accent-secondary);
  opacity: 0.9;
}

.logout-button {
  background: var(--accent-danger);
  border: none;
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background: var(--accent-danger);
  opacity: 0.9;
}