import { Routes, Route } from 'react-router-dom';
import { useState, useEffect } from 'react';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import AudioPlayer from './components/AudioPlayer';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import About from './pages/About';
import NotFound from './pages/NotFound';
import Explore from './pages/Explore';
import Account from './pages/Account';
import GenreList from './pages/GenreList';
import SelectedGenre from './pages/SelectedGenre';
import Login from './pages/Login';
import Signup from './pages/Signup';
import './app.css';
import MusicPlayer from './components/MusicPlayer';

function App() {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';

  // Sidebar collapse state with persistence
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Mobile drawer state
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);

  // Persist sidebar state
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isSidebarCollapsed));
  }, [isSidebarCollapsed]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const toggleMobileDrawer = () => {
    setIsMobileDrawerOpen(!isMobileDrawerOpen);
  };

  const closeMobileDrawer = () => {
    setIsMobileDrawerOpen(false);
  };

  return (
    <div className="App">
      <Navbar
        onToggleSidebar={toggleSidebar}
        onToggleMobileDrawer={toggleMobileDrawer}
        isMobileDrawerOpen={isMobileDrawerOpen}
        onCloseMobileDrawer={closeMobileDrawer}
      />
      <div className={`app-layout ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Sidebar isCollapsed={isSidebarCollapsed} isMobileOpen={isMobileDrawerOpen} />
        <main className={`main-content ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            } />
            <Route path="/about" element={
              <ProtectedRoute>
                <About />
              </ProtectedRoute>
            } />
            <Route path="/explore" element={
              <ProtectedRoute>
                <Explore />
              </ProtectedRoute>
            } />
            <Route path="/genres" element={
              <ProtectedRoute>
                <GenreList />
              </ProtectedRoute>
            } />
            <Route path="/genres/:genreId" element={
              <ProtectedRoute>
                <SelectedGenre />
              </ProtectedRoute>
            } />

            {/* Protected Routes - require authentication */}
            <Route path="/account" element={
              <ProtectedRoute requireAuth>
                <Account />
              </ProtectedRoute>
            } />

            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
      {/* <MusicPlayer /> */}
      <AudioPlayer />
    </div>
  );
}

export default App;